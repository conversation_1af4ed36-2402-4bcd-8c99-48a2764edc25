package com.howbuy.tms.high.batch.dao.mapper.customize.batch;

import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecFullPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 05文件记录全量表Mapper
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
public interface High05FileRecFullPoMapper {

    /**
     * 批量插入05文件记录
     * @param records 记录列表
     * @return 插入的记录数
     */
    int batchInsert(List<High05FileRecFullPo> records);

    /**
     * 根据产品代码和TA交易日期删除记录
     * @param fundCode 产品代码
     * @param taTradeDt TA交易日期
     * @return 删除的记录数
     */
    int deleteByFundCodeAndTaTradeDt(@Param("fundCode") String fundCode, @Param("taTradeDt") String taTradeDt);

    /**
     * 根据产品代码和TA交易日期查询记录
     * @param fundCode 产品代码
     * @param taTradeDt TA交易日期
     * @return 记录列表
     */
    List<High05FileRecFullPo> selectByFundCodeAndTaTradeDt(@Param("fundCode") String fundCode, @Param("taTradeDt") String taTradeDt);

    /**
     * 根据记录号查询记录
     * @param recordNo 记录号
     * @return 记录
     */
    High05FileRecFullPo selectByRecordNo(@Param("recordNo") String recordNo);

    /**
     * 插入单条记录
     * @param record 记录
     * @return 插入的记录数
     */
    int insertSelective(High05FileRecFullPo record);

    /**
     * 根据记录号更新记录
     * @param record 记录
     * @return 更新的记录数
     */
    int updateByRecordNo(High05FileRecFullPo record);

    /**
     * 根据记录号删除记录
     * @param recordNo 记录号
     * @return 删除的记录数
     */
    int deleteByRecordNo(@Param("recordNo") String recordNo);
}
