package com.howbuy.tms.high.batch.service.event.fund05file;

import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.batch.service.event.HighEvent;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @description: 05文件IO完成事件
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Getter
@Setter
@NoArgsConstructor
public class Fund05FileIoCompleteEvent extends HighEvent {

    /**
     * 产品代码
     */
    @MyValidation(fieldName = "产品代码", isRequired = true)
    private String fundCode;

    /**
     * TA交易日期
     */
    @MyValidation(fieldName = "TA交易日期", isRequired = true)
    private String taTradeDt;

    public Fund05FileIoCompleteEvent(String fundCode, String taTradeDt) {
        this.fundCode = fundCode;
        this.taTradeDt = taTradeDt;
    }
}
