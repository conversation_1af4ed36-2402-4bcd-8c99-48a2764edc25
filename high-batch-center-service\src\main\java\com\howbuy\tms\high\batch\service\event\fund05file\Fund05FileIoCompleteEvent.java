package com.howbuy.tms.high.batch.service.event.fund05file;

import org.springframework.context.ApplicationEvent;

/**
 * @description: 05文件IO完成事件
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
public class Fund05FileIoCompleteEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    public Fund05FileIoCompleteEvent(Object source) {
        super(source);
    }

    public Fund05FileIoCompleteEvent(String fundCode, String taTradeDt) {
        super(fundCode);
        this.fundCode = fundCode;
        this.taTradeDt = taTradeDt;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    @Override
    public String toString() {
        return "Fund05FileIoCompleteEvent{" +
                "fundCode='" + fundCode + '\'' +
                ", taTradeDt='" + taTradeDt + '\'' +
                '}';
    }
}
