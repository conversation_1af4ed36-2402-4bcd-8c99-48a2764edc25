1.com.howbuy.tms.high.batch.service.business.fundfilemonitor.FundFileMessageProcessor  这个是处理清算的文件通知消息类,ASS0009是05文件,文件是产品维度的全量数据,每次需要删除这个产品的全量io数据再保存
2.你参考com.howbuy.tms.high.batch.service.business.fundfilemonitor.FundAckFileProcessor,这个也是产品维度的数据,,写出05文件,落的io表,表实体你自己在dao层加上
3.io表落库了,你需要参考项目中事件发送处理机制,发送产品维度05文件io完成事件消息
4.io完成事件消息处理:将这个产品的io表数据写入正式05文件记录表
5.05文件正式表,表实体你自己按照rules加在dao上,,字段按照需求中的,注意加一个自增id,注意,写入正式表的时候不要用查询io表再bathInsert写入正式表, 因为数据太大,用insert into  select from io表的逻辑,在sql里面实现查询写入
