<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.batch.High05FileRecPoMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="record_no" jdbcType="VARCHAR" property="recordNo" />
        <result column="dtl_id" jdbcType="VARCHAR" property="dtlId" />
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
        <result column="cust_no" jdbcType="VARCHAR" property="custNo" />
        <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
        <result column="original_vol" jdbcType="DECIMAL" property="originalVol" />
        <result column="avail_vol" jdbcType="DECIMAL" property="availVol" />
        <result column="total_vol" jdbcType="DECIMAL" property="totalVol" />
        <result column="reg_dt" jdbcType="VARCHAR" property="regDt" />
        <result column="busi_source" jdbcType="VARCHAR" property="busiSource" />
        <result column="vol_lock_end_dt" jdbcType="VARCHAR" property="volLockEndDt" />
        <result column="report_dt" jdbcType="VARCHAR" property="reportDt" />
        <result column="ack_dt" jdbcType="VARCHAR" property="ackDt" />
        <result column="ta_ack_no" jdbcType="VARCHAR" property="taAckNo" />
        <result column="last_vol_lock_end_dt" jdbcType="VARCHAR" property="lastVolLockEndDt" />
        <result column="reg_dt_intervention_status" jdbcType="VARCHAR" property="regDtInterventionStatus" />
        <result column="vol_lock_end_dt_intervention_status" jdbcType="VARCHAR" property="volLockEndDtInterventionStatus" />
        <result column="check_status" jdbcType="VARCHAR" property="checkStatus" />
        <result column="ta_code" jdbcType="VARCHAR" property="taCode" />
        <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
        <result column="import_dt" jdbcType="VARCHAR" property="importDt" />
        <result column="create_dtm" jdbcType="TIMESTAMP" property="createDtm" />
        <result column="update_dtm" jdbcType="TIMESTAMP" property="updateDtm" />
    </resultMap>

    <sql id="Base_Column_List">
        id, record_no, dtl_id, fund_code, cust_no, fund_tx_acct_no, original_vol, avail_vol, 
        total_vol, reg_dt, busi_source, vol_lock_end_dt, report_dt, ack_dt, ta_ack_no, 
        last_vol_lock_end_dt, reg_dt_intervention_status, vol_lock_end_dt_intervention_status, 
        check_status, ta_code, sys_code, import_dt, create_dtm, update_dtm
    </sql>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO high_05_file_rec (
            record_no, dtl_id, fund_code, cust_no, fund_tx_acct_no, original_vol, avail_vol, 
            total_vol, reg_dt, busi_source, vol_lock_end_dt, report_dt, ack_dt, ta_ack_no, 
            last_vol_lock_end_dt, reg_dt_intervention_status, vol_lock_end_dt_intervention_status, 
            check_status, ta_code, sys_code, import_dt, create_dtm, update_dtm
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.recordNo}, #{item.dtlId}, #{item.fundCode}, #{item.custNo}, #{item.fundTxAcctNo}, 
                #{item.originalVol}, #{item.availVol}, #{item.totalVol}, #{item.regDt}, #{item.busiSource}, 
                #{item.volLockEndDt}, #{item.reportDt}, #{item.ackDt}, #{item.taAckNo}, #{item.lastVolLockEndDt}, 
                #{item.regDtInterventionStatus}, #{item.volLockEndDtInterventionStatus}, #{item.checkStatus}, 
                #{item.taCode}, #{item.sysCode}, #{item.importDt}, #{item.createDtm}, #{item.updateDtm}
            )
        </foreach>
    </insert>

    <!-- 根据产品代码和导入日期删除记录 -->
    <delete id="deleteByFundCodeAndImportDt">
        DELETE FROM high_05_file_rec 
        WHERE fund_code = #{fundCode} AND import_dt = #{importDt}
    </delete>

    <!-- 根据产品代码和导入日期查询记录 -->
    <select id="selectByFundCodeAndImportDt" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM high_05_file_rec
        WHERE fund_code = #{fundCode} AND import_dt = #{importDt}
        ORDER BY id
    </select>

    <!-- 根据记录号查询记录 -->
    <select id="selectByRecordNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM high_05_file_rec
        WHERE record_no = #{recordNo}
    </select>

    <!-- 根据产品代码查询记录 -->
    <select id="selectByFundCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM high_05_file_rec
        WHERE fund_code = #{fundCode}
        ORDER BY id
    </select>

    <!-- 根据客户号和产品代码查询记录 -->
    <select id="selectByCustNoAndFundCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM high_05_file_rec
        WHERE cust_no = #{custNo} AND fund_code = #{fundCode}
        ORDER BY id
    </select>

    <!-- 插入单条记录 -->
    <insert id="insertSelective" parameterType="com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo">
        INSERT INTO high_05_file_rec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordNo != null">record_no,</if>
            <if test="dtlId != null">dtl_id,</if>
            <if test="fundCode != null">fund_code,</if>
            <if test="custNo != null">cust_no,</if>
            <if test="fundTxAcctNo != null">fund_tx_acct_no,</if>
            <if test="originalVol != null">original_vol,</if>
            <if test="availVol != null">avail_vol,</if>
            <if test="totalVol != null">total_vol,</if>
            <if test="regDt != null">reg_dt,</if>
            <if test="busiSource != null">busi_source,</if>
            <if test="volLockEndDt != null">vol_lock_end_dt,</if>
            <if test="reportDt != null">report_dt,</if>
            <if test="ackDt != null">ack_dt,</if>
            <if test="taAckNo != null">ta_ack_no,</if>
            <if test="lastVolLockEndDt != null">last_vol_lock_end_dt,</if>
            <if test="regDtInterventionStatus != null">reg_dt_intervention_status,</if>
            <if test="volLockEndDtInterventionStatus != null">vol_lock_end_dt_intervention_status,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="taCode != null">ta_code,</if>
            <if test="sysCode != null">sys_code,</if>
            <if test="importDt != null">import_dt,</if>
            <if test="createDtm != null">create_dtm,</if>
            <if test="updateDtm != null">update_dtm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordNo != null">#{recordNo},</if>
            <if test="dtlId != null">#{dtlId},</if>
            <if test="fundCode != null">#{fundCode},</if>
            <if test="custNo != null">#{custNo},</if>
            <if test="fundTxAcctNo != null">#{fundTxAcctNo},</if>
            <if test="originalVol != null">#{originalVol},</if>
            <if test="availVol != null">#{availVol},</if>
            <if test="totalVol != null">#{totalVol},</if>
            <if test="regDt != null">#{regDt},</if>
            <if test="busiSource != null">#{busiSource},</if>
            <if test="volLockEndDt != null">#{volLockEndDt},</if>
            <if test="reportDt != null">#{reportDt},</if>
            <if test="ackDt != null">#{ackDt},</if>
            <if test="taAckNo != null">#{taAckNo},</if>
            <if test="lastVolLockEndDt != null">#{lastVolLockEndDt},</if>
            <if test="regDtInterventionStatus != null">#{regDtInterventionStatus},</if>
            <if test="volLockEndDtInterventionStatus != null">#{volLockEndDtInterventionStatus},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="taCode != null">#{taCode},</if>
            <if test="sysCode != null">#{sysCode},</if>
            <if test="importDt != null">#{importDt},</if>
            <if test="createDtm != null">#{createDtm},</if>
            <if test="updateDtm != null">#{updateDtm},</if>
        </trim>
    </insert>

    <!-- 根据记录号更新记录 -->
    <update id="updateByRecordNo" parameterType="com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo">
        UPDATE high_05_file_rec
        <set>
            <if test="dtlId != null">dtl_id = #{dtlId},</if>
            <if test="fundCode != null">fund_code = #{fundCode},</if>
            <if test="custNo != null">cust_no = #{custNo},</if>
            <if test="fundTxAcctNo != null">fund_tx_acct_no = #{fundTxAcctNo},</if>
            <if test="originalVol != null">original_vol = #{originalVol},</if>
            <if test="availVol != null">avail_vol = #{availVol},</if>
            <if test="totalVol != null">total_vol = #{totalVol},</if>
            <if test="regDt != null">reg_dt = #{regDt},</if>
            <if test="busiSource != null">busi_source = #{busiSource},</if>
            <if test="volLockEndDt != null">vol_lock_end_dt = #{volLockEndDt},</if>
            <if test="reportDt != null">report_dt = #{reportDt},</if>
            <if test="ackDt != null">ack_dt = #{ackDt},</if>
            <if test="taAckNo != null">ta_ack_no = #{taAckNo},</if>
            <if test="lastVolLockEndDt != null">last_vol_lock_end_dt = #{lastVolLockEndDt},</if>
            <if test="regDtInterventionStatus != null">reg_dt_intervention_status = #{regDtInterventionStatus},</if>
            <if test="volLockEndDtInterventionStatus != null">vol_lock_end_dt_intervention_status = #{volLockEndDtInterventionStatus},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="taCode != null">ta_code = #{taCode},</if>
            <if test="sysCode != null">sys_code = #{sysCode},</if>
            <if test="importDt != null">import_dt = #{importDt},</if>
            <if test="updateDtm != null">update_dtm = #{updateDtm},</if>
        </set>
        WHERE record_no = #{recordNo}
    </update>

    <!-- 根据记录号删除记录 -->
    <delete id="deleteByRecordNo">
        DELETE FROM high_05_file_rec WHERE record_no = #{recordNo}
    </delete>

</mapper>
