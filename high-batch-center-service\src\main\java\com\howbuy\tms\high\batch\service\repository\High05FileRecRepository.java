package com.howbuy.tms.high.batch.service.repository;

import com.google.common.collect.Lists;
import com.howbuy.tms.high.batch.dao.mapper.customize.batch.High05FileRecPoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 05文件记录正式表Repository
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Repository
public class High05FileRecRepository {

    @Autowired
    private High05FileRecPoMapper high05FileRecPoMapper;

    /**
     * 根据产品代码和导入日期删除记录
     * @param fundCode 产品代码
     * @param importDt 导入日期
     * @return 删除的记录数
     */
    public int deleteByFundCodeAndImportDt(String fundCode, String importDt) {
        return high05FileRecPoMapper.deleteByFundCodeAndImportDt(fundCode, importDt);
    }

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 插入的记录数
     */
    public void batchInsert(List<High05FileRecPo> records) {
        Lists.partition(records, 500).forEach(subList -> {
            high05FileRecPoMapper.batchInsert(subList);
        });
    }

    /**
     * 根据产品代码和导入日期查询记录
     * @param fundCode 产品代码
     * @param importDt 导入日期
     * @return 记录列表
     */
    public List<High05FileRecPo> selectByFundCodeAndImportDt(String fundCode, String importDt) {
        return high05FileRecPoMapper.selectByFundCodeAndImportDt(fundCode, importDt);
    }

    /**
     * 根据记录号查询记录
     * @param recordNo 记录号
     * @return 记录
     */
    public High05FileRecPo selectByRecordNo(String recordNo) {
        return high05FileRecPoMapper.selectByRecordNo(recordNo);
    }

    /**
     * 插入单条记录
     * @param record 记录
     * @return 插入的记录数
     */
    public int insert(High05FileRecPo record) {
        return high05FileRecPoMapper.insertSelective(record);
    }

    /**
     * 根据记录号更新记录
     * @param record 记录
     * @return 更新的记录数
     */
    public int updateByRecordNo(High05FileRecPo record) {
        return high05FileRecPoMapper.updateByRecordNo(record);
    }

    /**
     * 根据记录号删除记录
     * @param recordNo 记录号
     * @return 删除的记录数
     */
    public int deleteByRecordNo(String recordNo) {
        return high05FileRecPoMapper.deleteByRecordNo(recordNo);
    }

    /**
     * 根据产品代码查询记录
     * @param fundCode 产品代码
     * @return 记录列表
     */
    public List<High05FileRecPo> selectByFundCode(String fundCode) {
        return high05FileRecPoMapper.selectByFundCode(fundCode);
    }

    /**
     * 根据客户号和产品代码查询记录
     * @param custNo 客户号
     * @param fundCode 产品代码
     * @return 记录列表
     */
    public List<High05FileRecPo> selectByCustNoAndFundCode(String custNo, String fundCode) {
        return high05FileRecPoMapper.selectByCustNoAndFundCode(custNo, fundCode);
    }
}
