package com.howbuy.tms.high.batch.service.repository;

import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 05文件记录正式表Repository
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Repository
public interface High05FileRecRepository {

    /**
     * 根据产品代码和导入日期删除记录
     * @param fundCode 产品代码
     * @param importDt 导入日期
     * @return 删除的记录数
     */
    int deleteByFundCodeAndImportDt(String fundCode, String importDt);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 插入的记录数
     */
    int batchInsert(List<High05FileRecPo> records);

    /**
     * 根据产品代码和导入日期查询记录
     * @param fundCode 产品代码
     * @param importDt 导入日期
     * @return 记录列表
     */
    List<High05FileRecPo> selectByFundCodeAndImportDt(String fundCode, String importDt);

    /**
     * 根据记录号查询记录
     * @param recordNo 记录号
     * @return 记录
     */
    High05FileRecPo selectByRecordNo(String recordNo);

    /**
     * 插入单条记录
     * @param record 记录
     * @return 插入的记录数
     */
    int insert(High05FileRecPo record);

    /**
     * 根据记录号更新记录
     * @param record 记录
     * @return 更新的记录数
     */
    int updateByRecordNo(High05FileRecPo record);

    /**
     * 根据记录号删除记录
     * @param recordNo 记录号
     * @return 删除的记录数
     */
    int deleteByRecordNo(String recordNo);

    /**
     * 根据产品代码查询记录
     * @param fundCode 产品代码
     * @return 记录列表
     */
    List<High05FileRecPo> selectByFundCode(String fundCode);

    /**
     * 根据客户号和产品代码查询记录
     * @param custNo 客户号
     * @param fundCode 产品代码
     * @return 记录列表
     */
    List<High05FileRecPo> selectByCustNoAndFundCode(String custNo, String fundCode);
}
