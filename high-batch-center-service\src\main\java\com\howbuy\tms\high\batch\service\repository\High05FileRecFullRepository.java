package com.howbuy.tms.high.batch.service.repository;

import com.google.common.collect.Lists;
import com.howbuy.tms.high.batch.dao.mapper.customize.batch.High05FileRecFullPoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecFullPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 05文件记录全量表Repository
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Repository
public class High05FileRecFullRepository {

    @Autowired
    private High05FileRecFullPoMapper high05FileRecFullPoMapper;

    /**
     * 根据产品代码和TA交易日期删除记录
     * @param fundCode 产品代码
     * @param taTradeDt TA交易日期
     * @return 删除的记录数
     */
    public int deleteByFundCodeAndTaTradeDt(String fundCode, String taTradeDt) {
        return high05FileRecFullPoMapper.deleteByFundCodeAndTaTradeDt(fundCode, taTradeDt);
    }

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 插入的记录数
     */
    public void batchInsert(List<High05FileRecFullPo> records) {
        Lists.partition(records, 500).forEach(subList -> {
            high05FileRecFullPoMapper.batchInsert(subList);
        });
    }

    /**
     * 根据产品代码和TA交易日期查询记录
     * @param fundCode 产品代码
     * @param taTradeDt TA交易日期
     * @return 记录列表
     */
    public List<High05FileRecFullPo> selectByFundCodeAndTaTradeDt(String fundCode, String taTradeDt) {
        return high05FileRecFullPoMapper.selectByFundCodeAndTaTradeDt(fundCode, taTradeDt);
    }

    /**
     * 根据记录号查询记录
     * @param recordNo 记录号
     * @return 记录
     */
    public High05FileRecFullPo selectByRecordNo(String recordNo) {
        return high05FileRecFullPoMapper.selectByRecordNo(recordNo);
    }

    /**
     * 插入单条记录
     * @param record 记录
     * @return 插入的记录数
     */
    public int insert(High05FileRecFullPo record) {
        return high05FileRecFullPoMapper.insertSelective(record);
    }

    /**
     * 根据记录号更新记录
     * @param record 记录
     * @return 更新的记录数
     */
    public int updateByRecordNo(High05FileRecFullPo record) {
        return high05FileRecFullPoMapper.updateByRecordNo(record);
    }

    /**
     * 根据记录号删除记录
     * @param recordNo 记录号
     * @return 删除的记录数
     */
    public int deleteByRecordNo(String recordNo) {
        return high05FileRecFullPoMapper.deleteByRecordNo(recordNo);
    }
}
