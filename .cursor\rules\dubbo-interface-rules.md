---
description: 高端批处理中心Dubbo接口开发规范 (High-Batch-Center Dubbo Interface Development Guidelines)
globs: ["**/*Facade.java", "**/*Request.java", "**/*Response.java", "**/*Bean.java"]
alwaysApply: true
---

# 高端批处理中心Dubbo接口开发规范 (High-Batch-Center Dubbo Interface Development Guidelines)

## 项目信息 (Project Information)
- 项目名称: 高端批处理中心 (high-batch-center)
- 作者: 开发团队
- 版本: 1.0.0
- JDK版本: 1.8

## Dubbo接口生成规范 (Dubbo Interface Generation Standards)

### 接口包名与命名规范 (Package and Naming Conventions)
- **接口主包**: `com.howbuy.tms.high.batch.facade`
- **交易子包**: `com.howbuy.tms.high.batch.facade.trade.*` (具体业务再分子包)
- **查询子包**: `com.howbuy.tms.high.batch.facade.query.*` (具体业务再分子包)
- **接口命名**: 业务功能+`Facade` (如`QueryAccountBalanceFacade`)
- **接口继承**: `BaseFacade<请求类型, 响应类型>`
- **请求类型**: 业务功能+`Request`
- **响应类型**: 业务功能+`Response`

### 接口实现类规范 (Implementation Class Standards)
- **实现类包名**: `com.howbuy.tms.high.batch.service.facade`
- **实现类命名**: 接口名+`Service` (如`QueryAccountBalanceFacadeService`)
- **注解使用**:
  - `@DubboService` - 标识为Dubbo服务
  - `@Slf4j` - 用于日志记录
  - `@Component` - 注册为Spring组件

### 接口修改/新增规则 (Interface Modification/Addition Rules)
- **判断依据**:
  - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
  - 设计文档中接口名后标注"——新增"的，表示新增接口
  - 设计文档中接口名后无标注的，表示新增接口
- **修改原则**:
  - 修改接口时，保持原有的路径和方法名不变
  - 修改接口时，需要兼容原有的功能
  - 修改接口时，新增字段采用追加方式，不要删除或修改原有字段
  - 修改接口时，保持原有的注释格式，仅更新内容

## Dubbo接口实现规范 (Dubbo Interface Implementation Standards)

### 实现类模板 (Implementation Class Template)
```java
/**
 * @description: 接口实现功能描述
 * <AUTHOR>
 * @date yyyy/MM/dd HH:mm
 * @since JDK 1.8
 */
@Slf4j
@DubboService
@Component
public class XXXFacadeService implements XXXFacade {
 
    @Resource
    private XXXService xxxService;
     
    @Override
    public Response<XXXResponse> execute(XXXRequest request) {
        try {
            return Response.ok(xxxService.process(request));
        } catch (BusinessException e) {
            log.error("业务处理异常: {}", e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            return Response.fail("SYSTEM_ERROR", "系统异常");
        }
    }
}
```

### Service层交互规范 (Service Layer Interaction Standards)
- **对应Service命名**: 接口名+`Service` (如`QueryAccountBalanceService`)
- **Service包路径**: `com.howbuy.tms.high.batch.service.service.{业务子包}`
- **注入方式**: 使用`@Resource`注解
- **Service类规范**:
  - Service类直接实现业务逻辑，不需要创建接口和实现类
  - Service类使用`@Service`注解标识为Spring组件
  - Service类使用`@Slf4j`注解用于日志记录
  - Service类方法应包含完整的业务逻辑实现

## 请求对象规范 (Request Object Standards)

### 请求对象包名和命名 (Request Package and Naming)
- **请求对象包名**: `com.howbuy.tms.high.batch.facade.{业务子包}.request`
- **类命名**: 业务功能+`Request`
- **必须继承**: `BatchBaseRequest`或其他合适的基类
- **注解使用**: `@Setter/@Getter/@EqualsAndHashCode(callSuper = true)`

### 请求对象模板 (Request Object Template)
```java
/**
 * @description: 请求对象功能描述
 * <AUTHOR>
 * @date yyyy/MM/dd HH:mm
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class XXXRequest extends BatchBaseRequest {
 
    private static final long serialVersionUID = 1L;
     
    /**
     * 字段中文名称
     * 可选：附加说明（如：格式要求/取值范围）
     */
    private String fieldName;
    
    public XXXRequest() {
        super.setTxCode(TxCodes.XXX_TX_CODE);
    }
    
    // getter和setter方法
}
```

## 响应对象规范 (Response Object Standards)

### 响应对象包名和命名 (Response Package and Naming)
- **响应对象包名**: `com.howbuy.tms.high.batch.facade.{业务子包}.response`
- **类命名**: 业务功能+`Response`
- **注解使用**: `@Getter/@Setter`
- **序列化**: 实现`Serializable`接口，定义`serialVersionUID`

### 响应对象模板 (Response Object Template)
```java
/**
 * @description: 响应对象功能描述
 * <AUTHOR>
 * @date yyyy/MM/dd HH:mm
 * @since JDK 1.8
 */
@Getter
@Setter
public class XXXResponse implements Serializable {
 
    private static final long serialVersionUID = 1L;
     
    /**
     * 字段中文名称
     * 字段说明：每个字段必须添加中文注释
     */
    private String fieldName;
    
    // getter和setter方法
}
```

## Bean对象规范 (Bean Object Standards)

### Bean对象命名和结构 (Bean Object Naming and Structure)
- **Bean对象包名**: `com.howbuy.tms.high.batch.facade.{业务子包}.bean`
- **类命名**: 业务实体+`Bean` (如`CustBankBean`、`CustInfoBean`)
- **注解使用**: `@Getter/@Setter`
- **序列化**: 实现`Serializable`接口，定义`serialVersionUID`

### Bean对象模板 (Bean Object Template)
```java
/**
 * @description: Bean对象功能描述
 * <AUTHOR>
 * @date yyyy/MM/dd HH:mm
 * @since JDK 1.8
 */
@Getter
@Setter
public class XXXBean implements Serializable {
 
    private static final long serialVersionUID = 1L;
     
    /**
     * 字段中文名称
     * 字段说明和格式要求
     */
    private String fieldName;
    
    // getter和setter方法
}
```

## APIDOC注释规范 (APIDOC Documentation Standards)

### Dubbo接口APIDOC规范 (Dubbo Interface APIDOC Standards)
- **必须包含以下标签**:
  - `@api {get} dubbo:接口全类名.方法名() 接口编号-接口名称`
  - `@apiGroup 模块名称`
  - `@apiDescription 接口功能描述`
  - `@apiUse batchBaseRequest` (使用基础请求模板)
  - `@apiParam {类型} 参数名 参数说明`
  - `@apiParamExample {json} Request Example dubbo 响应类全类名`
  - `@apiUse batchBaseResponse` (使用基础响应模板)
  - `@apiSuccess {类型} 字段名 字段说明`
  - `@apiSuccess (分组名) {类型} 字段名 字段说明` (用于嵌套对象)

### APIDOC模板示例 (APIDOC Template Example)
```java
/**
 * @api {get} dubbo:com.howbuy.tms.high.batch.facade.query.queryacctbalance.
 *      QueryAccountBalanceFacade.execute() 12- 查询客户余额信息
 * @apiGroup high-batch-center
 * @apiDescription 查询客户余额信息
 * 
 * @apiUse batchBaseRequest
 * 
 * @apiParam {String} txAcctNo 交易账号
 * @apiParamExample {json} Request Example dubbo
 *                  com.howbuy.tms.high.batch.facade.query.queryacctbalance.
 *                  QueryAccountBalanceInfoResponse
 * 
 * @apiUse batchBaseResponse
 * 
 * @apiSuccess {CustInfoBean} custInfoBean 客户信息
 * @apiSuccess {List} custBankBeanList 客户银行卡列表
 * @apiSuccess {List} custFundVolBeanList 客户基金份额列表
 * @apiSuccess {List} custPortfolioVolBeanList 客户组合份额列表
 * 
 * @apiSuccess (custInfoBean) {String} custNo 客户号
 * @apiSuccess (custInfoBean) {String} invstType 客户类型
 * @apiSuccess (custInfoBean) {String} custName 客户姓名
 * @apiSuccess (custInfoBean) {String} idType 证件类型
 * @apiSuccess (custInfoBean) {String} idNo 证件号码
 * @apiSuccess (custInfoBean) {String} mobile 手机号
 * @apiSuccess (custInfoBean) {String} email 邮箱
 * @apiSuccess (custInfoBean) {String} custStatus 客户状态
 * 
 * @apiSuccess (custBankBeanList) {String} cpAcctNo 资金账号
 * @apiSuccess (custBankBeanList) {String} bankCode 银行编号
 * @apiSuccess (custBankBeanList) {String} bankAcct 银行账号
 * @apiSuccess (custBankBeanList) {String} bankAcctName 银行账户名称
 * @apiSuccess (custBankBeanList) {String} bankRegionName 分行名称
 * @apiSuccess (custBankBeanList) {String} bankAcctStatus 银行账户状态
 * @apiSuccess (custBankBeanList) {String} vrfyCardStat 卡验证状态
 */
public interface QueryAccountBalanceFacade extends BaseFacade<QueryAccountBalanceInfoRequest, QueryAccountBalanceInfoResponse> {
 
}
```

## 接口生成同步规则 (Interface Generation Synchronization Rules)

### 生成顺序 (Generation Order)
1. 根据接口文档在`high-batch-center-client`中生成接口定义
2. 在`high-batch-center-service`中生成对应的接口实现类
3. 在`high-batch-center-service`中生成对应的Service类（直接实现业务逻辑）
4. Service类中不要使用`@Transactional`注解，事务管理在repository层实现

### 同步原则 (Synchronization Principles)
- **接口名变更时**，实现类名也必须同步变更
- **接口方法变更时**，实现类方法也必须同步变更
- **接口参数变更时**，实现类参数也必须同步变更

## 特殊要求 (Special Requirements)

### APIDOC特殊要求 (APIDOC Special Requirements)
- 请求和响应示例必须包含真实业务场景数据
- 字段说明必须包含数据格式要求（如日期格式YYYYMMDD）
- 枚举值必须说明所有可能的取值（如1-电汇、2-支票）
- 金额字段必须说明精度
- 生成的apidoc在Facade接口定义上面一行，不要放到package或import上面

### 注释规范 (Comment Standards)
```java
/**
 * @description: 类的功能描述
 * <AUTHOR>
 * @date yyyy/MM/dd HH:mm
 * @since JDK 1.8
 */
```

### 字段注释规范 (Field Comment Standards)
```java
/**
 * 字段中文名称
 * 可选：附加说明（如：格式要求/取值范围）
 */
```

## 代码示例 (Code Examples)

### 完整接口定义示例 (Complete Interface Definition Example)
```java
/**
 * @api {get} dubbo:com.howbuy.tms.high.batch.facade.query.queryacctbalance.
 *      QueryAccountBalanceFacade.execute() 12- 查询客户余额信息
 * @apiGroup high-batch-center
 * @apiDescription 查询客户余额信息
 * 
 * @apiUse batchBaseRequest
 * 
 * @apiParam {String} txAcctNo 交易账号
 * @apiParamExample {json} Request Example dubbo
 *                  com.howbuy.tms.high.batch.facade.query.queryacctbalance.
 *                  QueryAccountBalanceInfoResponse
 * 
 * @apiUse batchBaseResponse
 * 
 * @apiSuccess {CustInfoBean} custInfoBean 客户信息
 * @apiSuccess {List} custBankBeanList 客户银行卡列表
 * @apiSuccess {List} custFundVolBeanList 客户基金份额列表
 * @apiSuccess {List} custPortfolioVolBeanList 客户组合份额列表
 */
public interface QueryAccountBalanceFacade extends BaseFacade<QueryAccountBalanceInfoRequest, QueryAccountBalanceInfoResponse> {
 
}
```

### 完整实现类示例 (Complete Implementation Class Example)
```java
/**
 * @description: 查询客户余额信息实现
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Slf4j
@DubboService
@Component
public class QueryAccountBalanceFacadeService implements QueryAccountBalanceFacade {
 
    @Resource
    private QueryAccountBalanceService queryAccountBalanceService;
     
    @Override
    public Response<QueryAccountBalanceInfoResponse> execute(QueryAccountBalanceInfoRequest request) {
        try {
            return Response.ok(queryAccountBalanceService.process(request));
        } catch (BusinessException e) {
            log.error("查询客户余额信息业务异常: {}", e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询客户余额信息系统异常: {}", e.getMessage(), e);
            return Response.fail("SYSTEM_ERROR", "系统异常");
        }
    }
}
```

## 生成指南 (Generation Guide)

1. **明确业务需求**，确定接口名称和功能
2. **在正确的包路径下创建接口**和对应的请求/响应类
3. **同步在service模块中创建对应的实现类**
4. **在service模块中创建对应的Service类**（直接实现业务逻辑，不需要接口）
5. **按规范编写APIDOC注释**，确保包含所有必要标签
6. **为请求对象的字段添加适当的验证注解**
7. **所有字段添加明确的中文注释**，说明用途和格式要求
8. **提供真实的请求和响应示例**
9. **对枚举值和特殊格式字段提供详细说明**

## 质量检查清单 (Quality Checklist)

- [ ] 接口命名符合规范（业务功能+Facade）
- [ ] 实现类命名符合规范（接口名+Service）
- [ ] 包路径正确
- [ ] 继承关系正确
- [ ] APIDOC注释完整
- [ ] 字段注释清晰
- [ ] 请求/响应对象结构合理
- [ ] Bean对象定义规范
- [ ] 异常处理完善
- [ ] 日志记录适当