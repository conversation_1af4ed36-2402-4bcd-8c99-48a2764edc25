package com.howbuy.tms.high.batch.service.service.file.fileimport.impl;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.utils.DateUtil;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecFullPo;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkUtil;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.High05FileRecFullRepository;
import com.howbuy.tms.high.batch.service.service.SequenceService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.AbstractFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 05文件导入服务
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Service
public class Fund05FileImportService extends AbstractFileImportService {

    private static final Logger logger = LogManager.getLogger(Fund05FileImportService.class);

    @Autowired
    private High05FileRecFullRepository high05FileRecFullRepository;

    @Autowired
    private SequenceService sequenceService;

    @Override
    public void process(FileImportContext context) throws Exception {
        logger.info("Fund05FileImportService-开始处理05文件导入, context:{}", JSON.toJSONString(context));

        String taCode = (String) context.getParams().get("taCode");
        String fundCode = (String) context.getParams().get("fundCode");
        String taTradeDt = context.getTaTradeDt();

        // 构建文件路径
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.ASSET_05_FILE);
        fileSdkPathInfo.setMiddlePath(context.getRelationPath());
        fileSdkPathInfo.setFileName(context.getFileName());

        String filePath = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
        logger.info("Fund05FileImportService-文件路径:{}", filePath);

        // 删除已存在的数据
        high05FileRecFullRepository.deleteByFundCodeAndTaTradeDt(fundCode, taTradeDt);

        // 读取并解析文件
        List<High05FileRecFullPo> records = parseFile(filePath, taCode, fundCode, taTradeDt);

        // 批量插入数据
        if (!records.isEmpty()) {
            high05FileRecFullRepository.batchInsert(records);
            logger.info("Fund05FileImportService-成功导入{}条05文件记录", records.size());
        }
    }

    /**
     * 解析05文件
     */
    private List<High05FileRecFullPo> parseFile(String filePath, String taCode, String fundCode, String taTradeDt) throws Exception {
        List<High05FileRecFullPo> records = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), "GBK"))) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                
                // 跳过空行
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                
                try {
                    High05FileRecFullPo record = parseLine(line, taCode, fundCode, taTradeDt);
                    if (record != null) {
                        records.add(record);
                    }
                } catch (Exception e) {
                    logger.error("Fund05FileImportService-解析第{}行数据失败, line:{}, error:{}", lineNumber, line, e.getMessage());
                    throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENETER_FUND_VOL_IMPORT_ERROR, 
                        "解析第" + lineNumber + "行数据失败: " + e.getMessage());
                }
            }
        }
        
        return records;
    }

    /**
     * 解析单行数据
     * 05文件格式：明细id|产品代码|客户号|基金交易账号|原始份额|可用份额|总份额|登记日期|业务来源|份额锁定结束日|上报日期|确认日期|TA确认流水号|上一个份额锁定结束日|登记日期干预状态|份额锁定结束日干预状态|05对账状态
     */
    private High05FileRecFullPo parseLine(String line, String taCode, String fundCode, String taTradeDt) {
        String[] fields = line.split("\\|", -1);
        
        if (fields.length < 17) {
            throw new RuntimeException("数据字段不足17个，实际字段数：" + fields.length);
        }

        High05FileRecFullPo record = new High05FileRecFullPo();
        Date now = new Date();
        
        // 生成记录号
        record.setRecordNo(sequenceService.getRecordNo("HIGH_05_FILE_RECORDNO_PREFIX", null));
        
        // 解析字段
        record.setDtlId(StringUtils.trim(fields[0]));
        record.setFundCode(StringUtils.trim(fields[1]));
        record.setCustNo(StringUtils.trim(fields[2]));
        record.setFundTxAcctNo(StringUtils.trim(fields[3]));
        
        // 解析金额字段
        record.setOriginalVol(parseBigDecimal(fields[4]));
        record.setAvailVol(parseBigDecimal(fields[5]));
        record.setTotalVol(parseBigDecimal(fields[6]));
        
        record.setRegDt(StringUtils.trim(fields[7]));
        record.setBusiSource(StringUtils.trim(fields[8]));
        record.setVolLockEndDt(StringUtils.trim(fields[9]));
        record.setReportDt(StringUtils.trim(fields[10]));
        record.setAckDt(StringUtils.trim(fields[11]));
        record.setTaAckNo(StringUtils.trim(fields[12]));
        record.setLastVolLockEndDt(StringUtils.trim(fields[13]));
        record.setRegDtInterventionStatus(StringUtils.trim(fields[14]));
        record.setVolLockEndDtInterventionStatus(StringUtils.trim(fields[15]));
        record.setCheckStatus(StringUtils.trim(fields[16]));
        
        // 设置系统字段
        record.setTaCode(taCode);
        record.setSysCode("HIGH");
        record.setAckImportDt(taTradeDt);
        record.setCreateDtm(now);
        record.setUpdateDtm(now);
        
        return record;
    }

    /**
     * 解析BigDecimal字段
     */
    private BigDecimal parseBigDecimal(String value) {
        if (StringUtils.isBlank(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            logger.warn("Fund05FileImportService-解析数字失败, value:{}", value);
            return BigDecimal.ZERO;
        }
    }
}
