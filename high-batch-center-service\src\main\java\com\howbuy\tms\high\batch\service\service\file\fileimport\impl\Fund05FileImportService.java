package com.howbuy.tms.high.batch.service.service.file.fileimport.impl;

import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecFullPo;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.High05FileRecFullRepository;
import com.howbuy.tms.high.batch.service.service.file.fileimport.AbstractFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description: 05文件导入服务
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Slf4j
@Service
public class Fund05FileImportService extends AbstractFileImportService<High05FileRecFullPo> {

    /**
     * 05文件字段分隔符
     */
    private static final String FIELD_SEPARATOR = "\\|";

    /**
     * 05文件字段数量
     */
    private static final int FIELD_COUNT = 17;

    @Autowired
    private High05FileRecFullRepository high05FileRecFullRepository;

    @Autowired
    private SequenceService sequenceService;

    @Override
    public High05FileRecFullPo parseLine(int index, String line, FileImportContext ctx) {
        Map<String, Object> params = ctx.getParams();
        String taCode = (String) params.get("taCode");
        String taTradeDt = ctx.getTaTradeDt();

        String[] fields = line.split(FIELD_SEPARATOR, -1);

        if (fields.length < FIELD_COUNT) {
            throw new BusinessException(ExceptionCodes.HIGH_BATCH_CENETER_FUND_VOL_IMPORT_ERROR,
                "05文件格式错误，字段数量不正确，期望" + FIELD_COUNT + "个字段，实际" + fields.length + "个字段");
        }

        High05FileRecFullPo record = new High05FileRecFullPo();
        Date now = new Date();

        // 生成记录号
        record.setRecordNo(sequenceService.getRecordNo(CacheKeyPrefix.HIGH_FILE_RECORDNO_PREFIX, StringUtils.trimToNull(fields[0])));

        // 解析字段
        record.setDtlId(StringUtils.trimToNull(fields[0]));
        record.setFundCode(StringUtils.trimToNull(fields[1]));
        record.setCustNo(StringUtils.trimToNull(fields[2]));
        record.setFundTxAcctNo(StringUtils.trimToNull(fields[3]));

        // 解析金额字段
        record.setOriginalVol(MathUtils.getBigDecimal(StringUtils.trimToNull(fields[4])));
        record.setAvailVol(MathUtils.getBigDecimal(StringUtils.trimToNull(fields[5])));
        record.setTotalVol(MathUtils.getBigDecimal(StringUtils.trimToNull(fields[6])));

        record.setRegDt(StringUtils.trimToNull(fields[7]));
        record.setBusiSource(StringUtils.trimToNull(fields[8]));
        record.setVolLockEndDt(StringUtils.trimToNull(fields[9]));
        record.setReportDt(StringUtils.trimToNull(fields[10]));
        record.setAckDt(StringUtils.trimToNull(fields[11]));
        record.setTaAckNo(StringUtils.trimToNull(fields[12]));
        record.setLastVolLockEndDt(StringUtils.trimToNull(fields[13]));
        record.setRegDtInterventionStatus(StringUtils.trimToNull(fields[14]));
        record.setVolLockEndDtInterventionStatus(StringUtils.trimToNull(fields[15]));
        record.setCheckStatus(StringUtils.trimToNull(fields[16]));

        // 设置系统字段
        record.setTaCode(taCode);
        record.setSysCode("HIGH");
        record.setAckImportDt(taTradeDt);
        record.setCreateDtm(now);
        record.setUpdateDtm(now);

        return record;
    }

    @Override
    public void deleteIoByParams(FileImportContext ctx) {
        Map<String, Object> params = ctx.getParams();
        String fundCode = (String) params.get("fundCode");
        String taTradeDt = ctx.getTaTradeDt();
        high05FileRecFullRepository.deleteByFundCodeAndTaTradeDt(fundCode, taTradeDt);
    }

    @Override
    public void batchSaveRecord(List<High05FileRecFullPo> list) {
        high05FileRecFullRepository.batchInsert(list);
    }

    @Override
    public String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.ASSET_05_FILE;
    }
}
