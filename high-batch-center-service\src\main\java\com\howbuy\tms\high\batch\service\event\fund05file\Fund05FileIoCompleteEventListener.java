package com.howbuy.tms.high.batch.service.event.fund05file;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecFullPo;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo;
import com.howbuy.tms.high.batch.service.event.HighEventListener;
import com.howbuy.tms.high.batch.service.repository.High05FileRecFullRepository;
import com.howbuy.tms.high.batch.service.repository.High05FileRecRepository;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 05文件IO完成事件监听器
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Component
@Slf4j
public class Fund05FileIoCompleteEventListener extends HighEventListener<Fund05FileIoCompleteEvent> {

    @Autowired
    private High05FileRecFullRepository high05FileRecFullRepository;

    @Autowired
    private High05FileRecRepository high05FileRecRepository;

    @Autowired
    private SequenceService sequenceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processEvent(Fund05FileIoCompleteEvent event) {
        log.info("Fund05FileIoCompleteEventListener-接收到05文件IO完成事件, event:{}", JSON.toJSONString(event));

        try {
            String fundCode = event.getFundCode();
            String taTradeDt = event.getTaTradeDt();

            // 查询IO表数据
            List<High05FileRecFullPo> ioRecords = high05FileRecFullRepository.selectByFundCodeAndTaTradeDt(fundCode, taTradeDt);

            if (ioRecords == null || ioRecords.isEmpty()) {
                log.warn("Fund05FileIoCompleteEventListener-未找到IO表数据, fundCode:{}, taTradeDt:{}", fundCode, taTradeDt);
                return;
            }

            // 删除正式表中已存在的数据
            high05FileRecRepository.deleteByFundCodeAndImportDt(fundCode, taTradeDt);

            // 将IO表数据转换为正式表数据
            List<High05FileRecPo> formalRecords = new ArrayList<>();
            Date now = new Date();

            for (High05FileRecFullPo ioRecord : ioRecords) {
                High05FileRecPo formalRecord = new High05FileRecPo();

                // 复制属性
                BeanUtils.copyProperties(ioRecord, formalRecord);

                // 重新生成ID和记录号
                formalRecord.setId(null);
                formalRecord.setRecordNo(sequenceService.getRecordNo(CacheKeyPrefix.HIGH_FILE_RECORDNO_PREFIX, null));

                // 设置导入日期
                formalRecord.setImportDt(taTradeDt);

                // 更新时间戳
                formalRecord.setCreateDtm(now);
                formalRecord.setUpdateDtm(now);

                formalRecords.add(formalRecord);
            }

            // 批量插入正式表
            if (!formalRecords.isEmpty()) {
                high05FileRecRepository.batchInsert(formalRecords);
                log.info("Fund05FileIoCompleteEventListener-成功迁移{}条05文件记录到正式表, fundCode:{}, taTradeDt:{}",
                    formalRecords.size(), fundCode, taTradeDt);
            }

        } catch (Exception e) {
            log.error("Fund05FileIoCompleteEventListener-处理05文件IO完成事件失败, event:{}, error:", JSON.toJSONString(event), e);
            throw e;
        }
    }
}
