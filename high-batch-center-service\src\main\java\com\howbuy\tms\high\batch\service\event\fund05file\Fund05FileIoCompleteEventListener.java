package com.howbuy.tms.high.batch.service.event.fund05file;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecFullPo;
import com.howbuy.tms.high.batch.dao.po.batch.High05FileRecPo;
import com.howbuy.tms.high.batch.service.repository.High05FileRecFullRepository;
import com.howbuy.tms.high.batch.service.repository.High05FileRecRepository;
import com.howbuy.tms.high.batch.service.service.SequenceService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 05文件IO完成事件监听器
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Component
public class Fund05FileIoCompleteEventListener implements ApplicationListener<Fund05FileIoCompleteEvent> {

    private static final Logger logger = LogManager.getLogger(Fund05FileIoCompleteEventListener.class);

    @Autowired
    private High05FileRecFullRepository high05FileRecFullRepository;

    @Autowired
    private High05FileRecRepository high05FileRecRepository;

    @Autowired
    private SequenceService sequenceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(Fund05FileIoCompleteEvent event) {
        logger.info("Fund05FileIoCompleteEventListener-接收到05文件IO完成事件, event:{}", JSON.toJSONString(event));

        try {
            String fundCode = event.getFundCode();
            String taTradeDt = event.getTaTradeDt();

            // 查询IO表数据
            List<High05FileRecFullPo> ioRecords = high05FileRecFullRepository.selectByFundCodeAndTaTradeDt(fundCode, taTradeDt);
            
            if (ioRecords == null || ioRecords.isEmpty()) {
                logger.warn("Fund05FileIoCompleteEventListener-未找到IO表数据, fundCode:{}, taTradeDt:{}", fundCode, taTradeDt);
                return;
            }

            // 删除正式表中已存在的数据
            high05FileRecRepository.deleteByFundCodeAndImportDt(fundCode, taTradeDt);

            // 将IO表数据转换为正式表数据
            List<High05FileRecPo> formalRecords = new ArrayList<>();
            Date now = new Date();

            for (High05FileRecFullPo ioRecord : ioRecords) {
                High05FileRecPo formalRecord = new High05FileRecPo();
                
                // 复制属性
                BeanUtils.copyProperties(ioRecord, formalRecord);
                
                // 重新生成ID和记录号
                formalRecord.setId(null);
                formalRecord.setRecordNo(sequenceService.getRecordNo("HIGH_05_FILE_FORMAL_RECORDNO_PREFIX", null));
                
                // 设置导入日期
                formalRecord.setImportDt(taTradeDt);
                
                // 更新时间戳
                formalRecord.setCreateDtm(now);
                formalRecord.setUpdateDtm(now);
                
                formalRecords.add(formalRecord);
            }

            // 批量插入正式表
            if (!formalRecords.isEmpty()) {
                high05FileRecRepository.batchInsert(formalRecords);
                logger.info("Fund05FileIoCompleteEventListener-成功迁移{}条05文件记录到正式表, fundCode:{}, taTradeDt:{}", 
                    formalRecords.size(), fundCode, taTradeDt);
            }

        } catch (Exception e) {
            logger.error("Fund05FileIoCompleteEventListener-处理05文件IO完成事件失败, event:{}, error:", JSON.toJSONString(event), e);
            throw e;
        }
    }
}
