package com.howbuy.tms.high.batch.service.config;

/**
 * @Description:文件路径配置
 * @Author: yun.lu
 * Date: 2024/1/16 16:34
 */
public class FilePathStoreBusinessCodeConfig {
    /**
     * 海外给高端文件
     */
    public static final String HK_CRM_TO_HIGH = "HK_CRM_TO_HIGH";
    /**
     * crm给中台的文件中间路径
     */
    public static final String CRM_TO_ZT = "CRM_TO_ZT";
    /**
     * 储蓄罐文件中间路径
     */
    public static final String FINAL_FILE_STORE="FINAL_FILE_STORE";

    /**
     * 给储蓄罐的文件路径的中间路径
     */
    public static final String FTX_REGTRAN_CURRENT_FILE_STORE="FTX_REFTANCURRENT_FILE_STORE";

    /**
     * 与资产中心份额对账文件中间路径
     */
    public static final String VOL_CHECK_FILE_PATH="VOL_CHECK_FILE_PATH";

    /**
     * 与资产中心黑名单文件中间路径
     */
    public static final String BLACK_CHECK_FILE_PATH="BLACK_CHECK_FILE_PATH";

    /**
     * 与资产中心na费文件中间路径
     */
    public static final String NAFEE_CHECK_FILE_PATH="NAFEE_CHECK_FILE_PATH";

    /**
     * 与资产中心确认文件中间路径
     */
    public static final String ACK_CHECK_FILE_PATH="ACK_CHECK_FILE_PATH";

    /**
     * 与资产中心yjyz文件中间路径
     */
    public static final String YJYZ_CHECK_FILE_PATH="YJYZ_CHECK_FILE_PATH";


    /**
     * 资金到账证明文件pdf生成路径
     */
    public static final String SIMU_MIDDLE_ARRIVAL_PROOF="SIMU_MIDDLE_ARRIVAL_PROOF";


    /**
     * 银行账号支付对账文件存放目录
     */
    public static final String FPS_PAYMENT="FPS_PAYMENT";

    /**
     * cxg支付对账文件存放目录
     */
    public static final String CXG_PAYMENT="CXG_PAYMENT";

    /**
     * 自定义签名文件生成路径
     * /data/pe/public/tradedoc/
     */
    public static final String PUBLIC_TRADEDOC="PUBLIC_TRADEDOC";

    /**
     * 回报表:/simu/middle/report/
     */
    public static final String SIMU_MIDDLE_REPORT="SIMU_MIDDLE_REPORT";

    /**
     * 投资经历证明字体文件中间路径:/custinvest
     */
    public static final String CUSTINVEST="CUSTINVEST";

    /**
     * 投资经历证明文件中间路径:/cc_certificate_file
     */
    public static final String CC_CERTIFICATE="CC_CERTIFICATE";


    /**
     * 给储蓄罐明细文件中间路径:/middle/finconsole/rept_succ_all_read
     */
    public static final String REPT_SUCC_ALL_READ="REPT_SUCC_ALL_READ";

    /**
     * 给储蓄罐结果文件中间路径:/middle/finconsole/cxg_buy_result
     */
    public static final String CXG_BUY_RESULT="CXG_BUY_RESULT";


    /**
     * 产品ok文件确认文件中间路径:/asset/ack
     */
    public static final String ASSET_ACK="ASSET_ACK";

    /**
     * 产品ok文件份额文件中间路径:/asset/vol
     */
    public static final String ASSET_VOL="ASSET_VOL";

    /**
     * 产品ok黑名单文件中间路径:/asset/vol
     */
    public static final String ASSET_BALDTLCHK="ASSET_BALDTLCHK";

    /**
     * 产品05文件中间路径:/asset/05file
     */
    public static final String ASSET_05_FILE="ASSET_05_FILE";

    /**
     * 份额确认书模板中间路径:/simu/middle/volpdftemplate
     */
    public static final String VOL_PDF_TEMPLATE="VOL_PDF_TEMPLATE";

    /**
     * 份额确认书生成路径中间路径:/simu/middle/volpdf/TACODE/FUNDCODE
     */
    public static final String VOL_PDF="VOL_PDF";

    /**
     * 风险揭示书原路径中间路径:data/pe/public/funddoc
     */
    public static final String PUBLIC_FUND_DOC="PUBLIC_FUND_DOC";

    /**
     * 空路径:/
     */
    public static final String TRADEOC_EMPTY_STORE="TRADEOC_EMPTY_STORE";

    /**
     * /send/SP
     */
    public static final String SEND_SP="SEND_SP";


    /**
     * /middle/retreat
     */
    public static final String MIDDLE_RETREAT="MIDDLE_RETREAT";

    public static final String FONT_FILE_PATH = "FONT_FILE_PATH";

    /**
     * 生成头寸文件给资金
     */
    public static final String CASH_TO_FIN_FILE="CASH_TO_FIN_FILE";
    /**
     * 生成头寸临时文件给资金
     */
    public static final String TMP_CASH_TO_FIN_FILE="TMP_CASH_TO_FIN_FILE";
    /**
     * 生成上报关系文件给资金
     */
    public static final String SUBMIT_SUCC_TO_FIN_FILE="SUBMIT_SUCC_TO_FIN_FILE";

    /**
     * 储蓄罐生成上报关系文件给资金
     */
    public static final String CXG_SUBMIT_SUCC_TO_FIN_FILE="CXG_SUBMIT_SUCC_TO_FIN_FILE";
    /**
     * 生成退款文件给资金
     */
    public static final String REFUND_TO_FIN_FILE="REFUND_TO_FIN_FILE";
    /**
     * 生成份额文件给机构
     */
    public static final String VOL_TO_INST_FILE="VOL_TO_INST_FILE";
    /**
     * 分红方式文件导入
     */
    public static final String IO_CUST_FUND_DIV_MODE_FILE="IO_CUST_FUND_DIV_MODE_FILE";

    /**
     * 储蓄罐文件
     */
    public static final String CXG_IMPORT_FILE="CXG_IMPORT_FILE";
}
