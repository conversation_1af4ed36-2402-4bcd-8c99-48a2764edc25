/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.fundfilemonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.dfile.HTextFileReader;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.FileOptionEnum;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.bean.TaInfoBean;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkUtil;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessRecRepository;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:OK文件处理
 * @date 2018年12月13日 上午9:33:01
 * @since JDK 1.6
 */
@Service("fundOKFileProcessor")
public class FundOKFileProcessor {
    private static Logger logger = LogManager.getLogger(FundOKFileProcessor.class);

    @Autowired
    private FundFileProcessRecRepository fundFileProcessRecRepository;

    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;

    public void process(FundFileMessage fundFileMessage) {
        // 处理ok文件
        parseOkFile(fundFileMessage);
    }

    private int parseOkFile(FundFileMessage fundFileMessage) {
        logger.info("FundOKFileProcessor-parseOkFile,开始解析后台给的ok文件,fundFileMessage={}", JSON.toJSON(fundFileMessage));
        String sdkBusinessCode = FilePathStoreBusinessCodeConfig.ASSET_ACK;
        String fileType = FileTypeEnum.H_TRADE_ACK.getCode();
        String fileOptionStatus = FileOpStatusEnum.Not.getKey();

        if ("ASS0001".equals(fundFileMessage.getDisFileType())) {
            sdkBusinessCode = FilePathStoreBusinessCodeConfig.ASSET_ACK;
            fileType = FileTypeEnum.H_TRADE_ACK.getCode();
            fileOptionStatus = FileOpStatusEnum.Not.getKey();
        } else if ("ASS0003".equals(fundFileMessage.getDisFileType())) {
            sdkBusinessCode = FilePathStoreBusinessCodeConfig.ASSET_VOL;
            fileType = FileTypeEnum.H_VOL_CHK.getCode();
            fileOptionStatus = FileOpStatusEnum.IMPORT_SUCCESSFUL.getKey();
        } else if ("ASS0004".equals(fundFileMessage.getDisFileType())) {
            sdkBusinessCode = FilePathStoreBusinessCodeConfig.ASSET_BALDTLCHK;
            fileType = FileTypeEnum.H_DTL_CHK.getCode();
            fileOptionStatus = FileOpStatusEnum.IMPORT_SUCCESSFUL.getKey();
        } else if ("ASS0009".equals(fundFileMessage.getDisFileType())) {
            sdkBusinessCode = FilePathStoreBusinessCodeConfig.ASSET_05_FILE;
            fileType = "H_05_FILE";
            fileOptionStatus = FileOpStatusEnum.IMPORT_SUCCESSFUL.getKey();
        }

        // 总记录数
        int recNum = 0;
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setBusinessCode(sdkBusinessCode);
        fileSdkPathInfo.setMiddlePath(fundFileMessage.getExportDt() + File.separator + fundFileMessage.getTaCode() + File.separator);
        fileSdkPathInfo.setFileName(fundFileMessage.getFileName());

        BusinessException ex = null;
        HTextFileReader reader = null;

        try {
            reader = FileSdkUtil.buildHTextFileReader(fileSdkPathInfo);
            String recLine = reader.readLine();
            if (StringUtils.isEmpty(recLine)) {
                // 无效的文件
                ex = new BusinessException(ExceptionCodes.BATCH_CENTER_FUND_ACK_OK_FILE_PROCESS_FAIL);
                throw ex;
            }
            // 总笔数|
            String[] content = recLine.split("\\|");
            recNum = Integer.parseInt(content[0]);
            reader.close();
        } catch (Exception e) {
            ex = new BusinessException(ExceptionCodes.BATCH_CENTER_FUND_ACK_OK_FILE_PROCESS_FAIL);
            ex.addSuppressed(e);
            logger.error("FundOKFileProcessor err:", e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    if (ex == null) {
                        ex = new BusinessException(ExceptionCodes.BATCH_CENTER_FUND_ACK_OK_FILE_PROCESS_FAIL);
                        ex.addSuppressed(e1);
                    }
                }
            }
            if (ex != null) {
                throw ex;
            }
        }

        // 保存文件消息
        FundFileProcessRecPo po = new FundFileProcessRecPo();
        po.setTaCode(fundFileMessage.getTaCode());
        po.setFundFileCount(new BigDecimal(recNum));
        po.setTaTradeDt(fundFileMessage.getExportDt());
        po.setFileType(fileType);
        po.setFileOption(FileOptionEnum.IMPORT.getCode());
        po.setFileOpStatus(fileOptionStatus);
        po.setProcessStatus(ProcessStatusEnum.NOT_PROCESS.getCode());
        po.setOperator(Constant.OPERATOR_SYS);
        try {
            po.setFileName(FileSdkUtil.getAbsolutePath(fileSdkPathInfo));
        } catch (Exception e) {
            logger.error("FundOKFileProcessor-获取文件绝对路径异常,e:" + e);
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_FUND_ACK_OK_FILE_PROCESS_FAIL);
        }
        fundFileProcessRecRepository.deleteByUniqueKey(po);
        fundFileProcessRecRepository.insert(po);
        return recNum;
    }
}
