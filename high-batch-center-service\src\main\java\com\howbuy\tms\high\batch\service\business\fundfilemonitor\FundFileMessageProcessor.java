/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.fundfilemonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.ProductChannelNewEnum;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.bean.TaInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @api {MQ} FDS_TRADE_ACK
 * @apiGroup message
 * @apiName 清算给高端确认文件消息
 * @apiDescription 清算给高端确认文件消息
 */
@Service("fundFileMessageProcessor")
public class FundFileMessageProcessor extends BatchMessageProcessor {
    // 等待时间 ms
    private static final long SLEEP_TIMES = 1000;
    private static final String DIV_CODE = "MID0004";
    private static final Logger logger = LogManager.getLogger(FundFileMessageProcessor.class);
    // 最大重试次数
    private final int MAX_RETRY_COUNT = 5;

    /**
     * 文件类型常量
     */
    private static final String ASS0001_FILE_TYPE = "ASS0001";
    private static final String ASS0003_FILE_TYPE = "ASS0003";
    private static final String ASS0004_FILE_TYPE = "ASS0004";
    private static final String ASS0009_FILE_TYPE = "ASS0009";
    @Resource(name = "fundFileProcessorMap")
    private Map<String, FundFileProcessor> fundFileProcessorMap;
    @Autowired
    private FundOKFileProcessor fundOKFileProcessor;
    @Autowired
    private WorkdayService workdayService;
    @Autowired
    private BusinessBatchFlowRepository businessBatchFlowRepository;
    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;
    /**
     * mq:FDS_TRADE_ACK
     */
    @Value("${queue.highFundFileMessageProcess}")
    private String queueName;

    /**
     * 是否调度
     *
     * @return
     */
    @Override
    public boolean isSchedule() {
        return false;
    }

    @Override
    protected String getQuartMessageChannel() {
        return queueName;
    }

    @Override
    public void doProcessMessage(SimpleMessage message) {

        String content = (String) message.getContent();
        logger.info("FundFileMessageProcessor-清算给高端的文件消息:{}", content);
        String[] contentArray = content.split("\\|");
        FundFileMessage fundFileMessage = new FundFileMessage();
        fundFileMessage.setExportDt(contentArray[0]);
        fundFileMessage.setDisFileType(contentArray[1]);
        fundFileMessage.setTaCode(contentArray[2]);
        fundFileMessage.setFileName(contentArray[3]);
        // 确认文件或者份额文件或者份额明细文件或者05文件会传基金代码其他不传
        if ((org.apache.commons.lang3.StringUtils.equals(ASS0001_FILE_TYPE, contentArray[1])
                || org.apache.commons.lang3.StringUtils.equals(ASS0003_FILE_TYPE, contentArray[1])
                || org.apache.commons.lang3.StringUtils.equals(ASS0004_FILE_TYPE, contentArray[1])
                || org.apache.commons.lang3.StringUtils.equals(ASS0009_FILE_TYPE, contentArray[1]))
                && !contentArray[3].contains("ok")) {
            fundFileMessage.setFundCode(contentArray[4]);
        }

        // 排除不是高端处理的文件类型
        FundFileProcessor fundFileProcessor = fundFileProcessorMap.get(fundFileMessage.getDisFileType());
        if (null == fundFileProcessor) {
            logger.error("不是高端处理的文件类型");
            return;
        }

        // 排除不是高端的ta
        if (!StringUtils.isEmpty(fundFileMessage.getTaCode())) {
            List<TaInfoBean> taList = queryTaInfoOuterService.getTaInfosByTaCode(fundFileMessage.getTaCode());
            if (CollectionUtils.isEmpty(taList)) {
                logger.error("FundFileMessageProcessor error: fundFileMessage.taList is null");
                return;
            }
            // 是否高端ta
            boolean highFlag = false;
            for (TaInfoBean bean : taList) {
                if (ProductChannelNewEnum.HIGH_FUND.getCode().equals(bean.getProductChannel())
                        || ProductChannelNewEnum.TP_SM.getCode().equals(bean.getProductChannel())) {
                    highFlag = true;
                    break;
                }
            }
            if (!highFlag) {
                logger.info("非高端ta：{}不处理", fundFileMessage.getTaCode());
                return;
            }
        }

        // 判断是否日终
        if (hasDayEnd(contentArray) && !DIV_CODE.equals(fundFileMessage.getDisFileType())) {
            logger.info("FundFileMessageProcessor-hasDayEnd,已经日终,不需要处理");
            OpsSysMonitor.warn("清算给的文件,因为已经日终,没有处理:" + content, OpsSysMonitor.INFO);
            return;
        }

        // 产品代码为空则认为是OK文件
        if (contentArray[3].contains("ok") && StringUtils.isEmpty(fundFileMessage.getFundCode())) {
            fundOKFileProcessor.process(fundFileMessage);
            return;
        }

        try {
            fundFileProcessor.process(fundFileMessage);
        } catch (Exception e) {
            logger.error("FundFileMessageProcessor|err", e);
            // 重新处理
            processErrRetry(fundFileMessage, fundFileProcessor);
        }
    }

    private boolean process(FundFileProcessor fundFileProcessor, FundFileMessage fundFileMessage) {
        try {
            fundFileProcessor.process(fundFileMessage);
        } catch (Exception e) {
            logger.error("FundFileMessageProcessor|err", e);
            return false;
        }

        return true;
    }

    /**
     * @Description 重启失败后，重新发起
     **/
    private void processErrRetry(FundFileMessage fundFileMessage, FundFileProcessor fundFileProcessor) {
        for (int count = 1; count <= MAX_RETRY_COUNT; count++) {
            long sleepTimes = SLEEP_TIMES * count;
            logger.info("FundFileMessageProcessor|retry times：{}, MAX_RETRY_COUNT:{}, sleep :{} ms", count, MAX_RETRY_COUNT, sleepTimes);

            // 等待500*count ms 后发起重试
            sleep(sleepTimes);

            boolean proceeRst = process(fundFileProcessor, fundFileMessage);
            if (proceeRst) {
                break;
            }
            if (count == MAX_RETRY_COUNT) {
                String msg = String.format("份额文件导入，重试:%d次失败，fundFileMessage:%s", MAX_RETRY_COUNT, JSON.toJSONString(fundFileMessage));
                OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);
            }

        }

    }

    private void sleep(long timeOut) {
        try {
            // 等待500ms后发起重试
            Thread.sleep(timeOut);

        } catch (InterruptedException e) {
            logger.error("FundFileMessageProcessor sleep error:", e);
            Thread.currentThread().interrupt();
        }
    }

    public boolean hasDayEnd(String[] array) {
        String needForceImport = array.length == 6 ? array[5] : null;
        if (StringUtils.isNotBlank(needForceImport) && YesOrNoEnum.YES.getCode().equals(needForceImport)) {
            logger.info("不看日终,强制执行导入");
            return false;
        }
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        String appDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        logger.info("FundFileMessageProcessor|hasDayEnd tradeDt:{}, currDt：{}", tradeDt, appDt);
        if (tradeDt == null || tradeDt.compareTo(appDt) < 0) {
            return false;
        }
        BusinessBatchFlowPo businessBatchFlowPo = businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(BusinessProcessingStepEnum.BPS_DAY_END_PROCESS.getCode(),
                tradeDt, SysCodeEnum.BATCH_HIGH.getCode());

        if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(businessBatchFlowPo.getBatchStat())) {
            logger.info("tradeDt:{} has day end , will not process files", tradeDt);
            return true;
        } else {
            return false;
        }
    }
}
