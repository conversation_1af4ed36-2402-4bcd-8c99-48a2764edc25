package com.howbuy.tms.high.batch.service.business.fundfilemonitor;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.FileOptionEnum;
import com.howbuy.tms.common.enums.database.FileOptionStatus;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessDtlRecPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkUtil;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.event.HighEventPublisher;
import com.howbuy.tms.high.batch.service.event.fund05file.Fund05FileIoCompleteEvent;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessDtlRecRepository;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.Fund05FileImportService;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @description: 05文件处理器
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Service("fund05FileProcessor")
@Slf4j
public class Fund05FileProcessor implements FundFileProcessor {

    /**
     * 05文件类型
     */
    private static final String H_05_FILE_TYPE = "H_05_FILE";

    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private FundFileProcessDtlRecRepository fundFileProcessDtlRecRepository;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private Fund05FileImportService fund05FileImportService;

    @Autowired
    private HighEventPublisher highEventPublisher;

    @Override
    public void process(FundFileMessage fundFileMessage) throws Exception {
        log.info("Fund05FileProcessor-开始处理05文件消息, fundFileMessage:{}", JSON.toJSONString(fundFileMessage));
        String taCode = fundFileMessage.getTaCode();
        String fundCode = fundFileMessage.getFundCode();
        String taTradeDt = fundFileMessage.getExportDt();
        String fileType = H_05_FILE_TYPE;

        // 查询所高端(专户/私募)TA
        Set<String> taCodeList = queryTaInfoOuterService.getAllHighTaCode();
        // 判断当天TA文件是否是需要处理，如果是，保存文件消息；否则忽略该消息
        if (taCodeList.contains(taCode)) {
            // 查询该TA下所有高端产品
            List<String> productList = new ArrayList<>();
            List<HighProductBaseInfoModel> beanList = queryHighProductOuterService.getHighProductBaseInfoByTaCode(taCode);
            if (CollectionUtils.isNotEmpty(beanList)) {
                for (HighProductBaseInfoModel model : beanList) {
                    productList.add(model.getFundCode());
                }
            }
            log.info("Fund05FileProcessor-查询产品结果,taCode:{},productList:{}", taCode, JSON.toJSONString(productList));

            FundFileProcessDtlRecPo file = fundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(taTradeDt, fileType, taCode, fundCode);
            if (file != null) {
                if (FileOptionStatus.PROCESSING.getCode().equals(file.getFileOpStatus())) {
                    log.info("Fund05FileProcessor.FundFileProcessDtlRecPo is processing|file:{}", JSON.toJSONString(file));
                    throw new BatchException(ExceptionCodes.HIGH_BATCH_CENETER_FUND_VOL_IMPORT_ERROR, "文件正在处理中,不能导入");
                }
                log.info("Fund05FileProcessor-查询到文件明细,但是不是处理中,file:{}", JSON.toJSONString(file));
            }

            // 保存文件消息
            FundFileProcessDtlRecPo po = new FundFileProcessDtlRecPo();
            po.setTaTradeDt(taTradeDt);
            po.setFileType(fileType);
            po.setTaCode(taCode);
            po.setFundCode(fundCode);
            po.setFileOption(FileOptionEnum.IMPORT.getCode());
            // 直接默认处理中
            po.setFileOpStatus(FileOpStatusEnum.PROCESSING.getKey());
            po.setOperator(Constant.OPERATOR_SYS);

            FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
            fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.ASSET_05_FILE);
            fileSdkPathInfo.setMiddlePath(fundFileMessage.getExportDt() + File.separator + taCode + File.separator);
            fileSdkPathInfo.setFileName(fundFileMessage.getFileName());
            po.setFileName(FileSdkUtil.getAbsolutePath(fileSdkPathInfo));

            Date date = new Date();
            po.setCreateDtm(date);
            po.setUpdateDtm(date);
            po.setRecordNo(sequenceService.getRecordNo("HIGH_FILE_RECORDNO_PREFIX", null));

            fundFileProcessDtlRecRepository.deleteByUniqueKey(po);
            fundFileProcessDtlRecRepository.insert(po);

            FundFileProcessDtlRecPo updatePo = null;
            try {
                if (productList.contains(fundCode)) {
                    // 解析并保存文件内容到全量库
                    log.info("开始导入高端05文件-fileSdkPathInfo:{},taCode:{}", JSON.toJSONString(fileSdkPathInfo), taCode);
                    FileImportContext context = new FileImportContext();
                    context.setFileName(fundFileMessage.getFileName());
                    context.setRelationPath(fundFileMessage.getExportDt() + File.separator + taCode + File.separator);
                    context.setTaTradeDt(taTradeDt);
                    context.getParams().put("taCode", taCode);
                    context.getParams().put("fundCode", fundCode);

                    // 导入05文件到IO表
                    fund05FileImportService.process(context);

                    // 发送05文件IO完成事件
                    Fund05FileIoCompleteEvent event = new Fund05FileIoCompleteEvent(fundCode, taTradeDt);
                    highEventPublisher.publishEvent(event);
                    log.info("Fund05FileProcessor-发送05文件IO完成事件, fundCode:{}, taTradeDt:{}", fundCode, taTradeDt);
                }

                // 更新文件处理状态
                updatePo = new FundFileProcessDtlRecPo();
                updatePo.setRecordNo(po.getRecordNo());
                // 生成成功
                updatePo.setFileOpStatus(Constant.FILE_OP_STATUS_GEN_SUCC);
                updatePo.setUpdateDtm(date);
                fundFileProcessDtlRecRepository.updateByRecordNo(updatePo);
            } catch (Exception e) {
                updatePo = new FundFileProcessDtlRecPo();
                // 生成失败
                updatePo.setFileOpStatus(Constant.FILE_OP_STATUS_GEN_FAIL);
                if (e instanceof BusinessException) {
                    updatePo.setMemo(MessageSource.getMessageByCode(((BusinessException) e).getErrorCode()));
                } else {
                    updatePo.setMemo("文件处理发生异常");
                }
                updatePo.setRecordNo(po.getRecordNo());
                updatePo.setUpdateDtm(date);
                fundFileProcessDtlRecRepository.updateByRecordNo(updatePo);
                log.error("Fund05FileProcessor-文件处理发生异常,e:", e);
                throw e;
            }
        }
    }
}
