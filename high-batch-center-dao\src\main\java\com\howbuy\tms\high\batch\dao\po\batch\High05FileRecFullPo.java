package com.howbuy.tms.high.batch.dao.po.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 05文件记录全量表实体类
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
public class High05FileRecFullPo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 记录号
     */
    private String recordNo;

    /**
     * 明细id
     */
    private String dtlId;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 客户号
     */
    private String custNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 原始份额
     */
    private BigDecimal originalVol;

    /**
     * 可用份额
     */
    private BigDecimal availVol;

    /**
     * 总份额
     */
    private BigDecimal totalVol;

    /**
     * 登记日期
     */
    private String regDt;

    /**
     * 业务来源
     */
    private String busiSource;

    /**
     * 份额锁定结束日
     */
    private String volLockEndDt;

    /**
     * 上报日期
     */
    private String reportDt;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * TA确认流水号
     */
    private String taAckNo;

    /**
     * 上一个份额锁定结束日
     */
    private String lastVolLockEndDt;

    /**
     * 登记日期干预状态
     */
    private String regDtInterventionStatus;

    /**
     * 份额锁定结束日干预状态
     */
    private String volLockEndDtInterventionStatus;

    /**
     * 05对账状态
     */
    private String checkStatus;

    /**
     * TA代码
     */
    private String taCode;

    /**
     * 系统代码
     */
    private String sysCode;

    /**
     * 确认导入日期
     */
    private String ackImportDt;

    /**
     * 创建时间
     */
    private Date createDtm;

    /**
     * 更新时间
     */
    private Date updateDtm;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo == null ? null : recordNo.trim();
    }

    public String getDtlId() {
        return dtlId;
    }

    public void setDtlId(String dtlId) {
        this.dtlId = dtlId == null ? null : dtlId.trim();
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode == null ? null : fundCode.trim();
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo == null ? null : custNo.trim();
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo == null ? null : fundTxAcctNo.trim();
    }

    public BigDecimal getOriginalVol() {
        return originalVol;
    }

    public void setOriginalVol(BigDecimal originalVol) {
        this.originalVol = originalVol;
    }

    public BigDecimal getAvailVol() {
        return availVol;
    }

    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }

    public BigDecimal getTotalVol() {
        return totalVol;
    }

    public void setTotalVol(BigDecimal totalVol) {
        this.totalVol = totalVol;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt == null ? null : regDt.trim();
    }

    public String getBusiSource() {
        return busiSource;
    }

    public void setBusiSource(String busiSource) {
        this.busiSource = busiSource == null ? null : busiSource.trim();
    }

    public String getVolLockEndDt() {
        return volLockEndDt;
    }

    public void setVolLockEndDt(String volLockEndDt) {
        this.volLockEndDt = volLockEndDt == null ? null : volLockEndDt.trim();
    }

    public String getReportDt() {
        return reportDt;
    }

    public void setReportDt(String reportDt) {
        this.reportDt = reportDt == null ? null : reportDt.trim();
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt == null ? null : ackDt.trim();
    }

    public String getTaAckNo() {
        return taAckNo;
    }

    public void setTaAckNo(String taAckNo) {
        this.taAckNo = taAckNo == null ? null : taAckNo.trim();
    }

    public String getLastVolLockEndDt() {
        return lastVolLockEndDt;
    }

    public void setLastVolLockEndDt(String lastVolLockEndDt) {
        this.lastVolLockEndDt = lastVolLockEndDt == null ? null : lastVolLockEndDt.trim();
    }

    public String getRegDtInterventionStatus() {
        return regDtInterventionStatus;
    }

    public void setRegDtInterventionStatus(String regDtInterventionStatus) {
        this.regDtInterventionStatus = regDtInterventionStatus == null ? null : regDtInterventionStatus.trim();
    }

    public String getVolLockEndDtInterventionStatus() {
        return volLockEndDtInterventionStatus;
    }

    public void setVolLockEndDtInterventionStatus(String volLockEndDtInterventionStatus) {
        this.volLockEndDtInterventionStatus = volLockEndDtInterventionStatus == null ? null : volLockEndDtInterventionStatus.trim();
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus == null ? null : checkStatus.trim();
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode == null ? null : taCode.trim();
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode == null ? null : sysCode.trim();
    }

    public String getAckImportDt() {
        return ackImportDt;
    }

    public void setAckImportDt(String ackImportDt) {
        this.ackImportDt = ackImportDt == null ? null : ackImportDt.trim();
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }
}
